import 'package:flutter/material.dart';
import 'package:neubrutalism_ui/neubrutalism_ui.dart';
import 'package:wicker/widgets/user_contributions_tab.dart';
import 'package:wicker/widgets/user_playlists_tab.dart';
import 'package:eva_icons_flutter/eva_icons_flutter.dart';

class HubScreen extends StatefulWidget {
  const HubScreen({super.key});

  @override
  _HubScreenState createState() => _HubScreenState();
}

class _HubScreenState extends State<HubScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          "user_name",
          style: TextStyle(color: Colors.black, fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(EvaIcons.settings2Outline, color: Colors.black),
            onPressed: () {
              /* TODO: Navigate to settings */
            },
          ),
        ],
      ),
      backgroundColor: Colors.white,
      body: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) {
          return [SliverToBoxAdapter(child: _buildProfileHeader())];
        },
        body: Column(
          children: [
            TabBar(
              controller: _tabController,
              labelColor: Colors.black,
              unselectedLabelColor: Colors.grey,
              indicatorColor: Colors.black,
              tabs: const [
                Tab(icon: Icon(EvaIcons.grid)),
                Tab(icon: Icon(EvaIcons.bookmark)),
              ],
            ),
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: const [UserContributionsTab(), UserPlaylistsTab()],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Builds the top section of the profile page
  Widget _buildProfileHeader() {
    // Mock data for the profile
    const String username = "Gaud_King";
    const String bio = "Discovering the best of Accra, one kenkey at a time.";
    const int contributions = 12;
    const int followers = 336;
    const int following = 230;

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              NeuCard(
                cardColor: Colors.white,
                borderRadius: BorderRadius.all(Radius.circular(50)),
                child: SizedBox(
                  height: 80,
                  width: 80,
                  child: CircleAvatar(
                    backgroundImage: NetworkImage(
                      'https://picsum.photos/seed/profile/200',
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 20),
              Expanded(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    _buildStatColumn("Posts", contributions.toString()),
                    _buildStatColumn("Followers", followers.toString()),
                    _buildStatColumn("Following", following.toString()),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            username,
            style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
          ),
          Text(bio, style: const TextStyle(color: Colors.black87)),
          const SizedBox(height: 16),
          _buildActionButtons(),
        ],
      ),
    );
  }

  /// Builds the row of action buttons (Edit Profile, Share Profile)
  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: NeuTextButton(
            onPressed: () {},
            buttonColor: Colors.grey.shade200,
            enableAnimation: true,
            text: Text(
              'Edit Profile',
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: NeuTextButton(
            onPressed: () {},
            enableAnimation: true,
            buttonColor: Colors.grey.shade200,
            text: Text("Share Profile"),
          ),
        ),
      ],
    );
  }

  /// Helper for creating the stat columns (Posts, Followers, etc.)
  Widget _buildStatColumn(String label, String count) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          count,
          style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 18),
        ),
        Text(label, style: const TextStyle(color: Colors.grey)),
      ],
    );
  }
}
