import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:eva_icons_flutter/eva_icons_flutter.dart';

class PlaceDetailCard extends StatelessWidget {
  final Map<String, dynamic> placeData;
  final VoidCallback onClose;

  const PlaceDetailCard({
    super.key,
    required this.placeData,
    required this.onClose,
  });

  @override
  Widget build(BuildContext context) {
    String imageUrl = 'https://via.placeholder.com/150.png?text=No+Image';
    if (placeData['photos'] != null &&
        (placeData['photos'] as List).isNotEmpty) {
      final String baseUrl = defaultTargetPlatform == TargetPlatform.android
          ? "http://********:5000"
          : "http://127.0.0.1:5000";
      String imagePath = placeData['photos'][0];
      String correctedPath = imagePath.replaceAll('\\', '/');
      imageUrl = '$baseUrl/$correctedPath';
    }

    return Card(
      margin: const EdgeInsets.fromLTRB(16, 0, 16, 32),
      elevation: 8,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            leading: ClipRRect(
              borderRadius: BorderRadius.circular(8.0),
              child: Image.network(
                imageUrl,
                width: 60,
                height: 60,
                fit: BoxFit.cover,
              ),
            ),
            title: Text(
              placeData['name'],
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            subtitle: Text(placeData['category']),
            trailing: IconButton(
              icon: const Icon(EvaIcons.closeCircle),
              onPressed: onClose,
            ),
          ),
          const Divider(height: 1),
          // NEW: Engagement buttons row
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 4.0, horizontal: 8.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildActionButton(
                  icon: EvaIcons.arrowUpwardOutline,
                  label: "Like",
                  onTap: () {},
                ),
                _buildActionButton(
                  icon: EvaIcons.messageSquareOutline,
                  label: "Comment",
                  onTap: () {},
                ),
                _buildActionButton(
                  icon: EvaIcons.shareOutline,
                  label: "Share",
                  onTap: () {},
                ),
                _buildActionButton(
                  icon: EvaIcons.bookmarkOutline,
                  label: "Save",
                  onTap: () {},
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Helper widget for buttons to reduce code duplication
  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return TextButton.icon(
      icon: Icon(icon, size: 20),
      label: Text(label),
      onPressed: onTap,
      style: TextButton.styleFrom(
        foregroundColor: Colors.black54,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      ),
    );
  }
}
