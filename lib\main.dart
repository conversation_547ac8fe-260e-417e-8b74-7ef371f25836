import 'package:flutter/material.dart';
import 'package:eva_icons_flutter/eva_icons_flutter.dart';
import 'screens/home_screen.dart';
import 'screens/explore_screen.dart';
import 'screens/hub_screen.dart';
import 'screens/login_screen.dart';
import 'services/auth_service.dart';
import 'screens/add_place_screen.dart';
import 'screens/create_playlist_screen.dart';
import 'screens/create_queue_screen.dart';
import 'screens/create_post_screen.dart';
import 'package:neubrutalism_ui/neubrutalism_ui.dart';

void main() {
  runApp(const WickerApp());
}

class WickerApp extends StatelessWidget {
  const WickerApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Wicker App',
      theme: ThemeData(
        primarySwatch: Colors.teal, // A slightly different theme color
        visualDensity: VisualDensity.adaptivePlatformDensity,
      ),
      debugShowCheckedModeBanner: false,
      home: const AuthWrapper(), // Start with the AuthWrapper
    );
  }
}

// This widget will decide which screen to show
class AuthWrapper extends StatefulWidget {
  const AuthWrapper({super.key});

  @override
  _AuthWrapperState createState() => _AuthWrapperState();
}

class _AuthWrapperState extends State<AuthWrapper> {
  final AuthService _authService = AuthService();

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<String?>(
      future: _authService.getToken(), // Check for an existing token
      builder: (context, snapshot) {
        // While checking, show a loading indicator
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Scaffold(
            body: Center(child: CircularProgressIndicator()),
          );
        }

        // If a token exists, go to MainScreen
        if (snapshot.hasData && snapshot.data != null) {
          return const MainScreen();
        }

        // Otherwise, go to LoginScreen
        return const LoginScreen();
      },
    );
  }
}

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  _MainScreenState createState() => _MainScreenState();
}

// Replace the entire class below
class _MainScreenState extends State<MainScreen> {
  int _currentIndex = 0;

  // The screens list no longer needs the ContributeScreen placeholder
  late final List<Widget> _screens = [
    HomeScreen(onSearchTap: () => onTabTapped(1)),
    const ExploreScreen(),
    // The HubScreen is now at index 2 in this list
    const HubScreen(),
  ];

  // This is the function that shows our bottom sheet
  void _showContributeBottomSheet() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white,
      builder: (context) {
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 24.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              NeuIconButton(
                enableAnimation: true,
                icon: const Icon(EvaIcons.pinOutline),
                onPressed: () {
                  Navigator.pop(context);
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const AddPlaceScreen(),
                    ),
                  );
                },
              ),
              NeuIconButton(
                enableAnimation: true,
                icon: const Icon(EvaIcons.messageSquareOutline),
                onPressed: () {
                  Navigator.pop(context);
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const CreatePostScreen(),
                    ),
                  );
                },
              ),
              NeuIconButton(
                enableAnimation: true,
                icon: const Icon(EvaIcons.bookmarkOutline),
                onPressed: () {
                  Navigator.pop(context);
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const CreateQueueScreen(),
                    ),
                  );
                },
              ),
              NeuIconButton(
                enableAnimation: true,
                icon: const Icon(EvaIcons.browserOutline),
                onPressed: () {
                  Navigator.pop(context);
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const CreatePlaylistScreen(),
                    ),
                  );
                },
              ),
            ],
          ),
        );
      },
    );
  }

  void onTabTapped(int index) {
    // If the 'Contribute' button is tapped (index 1 in the bar items)
    if (index == 1) {
      _showContributeBottomSheet();
    } else {
      // For other tabs, we need to adjust the index to match our _screens list
      // Home (0) -> 0
      // Explore (2) -> 1
      // Hub (3) -> 2
      int newIndex = index > 1 ? index - 1 : index;
      setState(() {
        _currentIndex = newIndex;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // The app bar is removed from here since each screen can have its own
      body: IndexedStack(index: _currentIndex, children: _screens),
      bottomNavigationBar: BottomNavigationBar(
        // We manually calculate the highlighted index
        currentIndex: _currentIndex > 0 ? _currentIndex + 1 : _currentIndex,
        onTap: onTabTapped,
        selectedItemColor: Colors.teal,
        unselectedItemColor: Colors.grey,
        showUnselectedLabels: true,
        type: BottomNavigationBarType.fixed,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(EvaIcons.homeOutline),
            activeIcon: Icon(EvaIcons.home),
            label: 'Home',
          ),
          // This is the "+" button. It doesn't have a screen, only an action.
          BottomNavigationBarItem(
            icon: Icon(EvaIcons.plusCircleOutline, size: 32),
            label: 'Contribute',
          ),
          BottomNavigationBarItem(
            icon: Icon(EvaIcons.searchOutline),
            activeIcon: Icon(EvaIcons.search),
            label: 'Explore',
          ),
          BottomNavigationBarItem(
            icon: Icon(EvaIcons.personOutline),
            activeIcon: Icon(EvaIcons.person),
            label: 'Hub',
          ),
        ],
      ),
    );
  }
}
