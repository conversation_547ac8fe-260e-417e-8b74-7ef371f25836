import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:eva_icons_flutter/eva_icons_flutter.dart';
import 'package:wicker/widgets/comments_modal.dart';
import 'package:wicker/widgets/add_to_playlist_modal.dart';
import 'package:wicker/widgets/aspect_ratio_container.dart';
import 'package:flutter/foundation.dart';
import 'package:wicker/widgets/media_player.dart';

class PostCard extends StatefulWidget {
  final Map<String, dynamic> postData;

  const PostCard({super.key, required this.postData});

  @override
  _PostCardState createState() => _PostCardState();
}

class _PostCardState extends State<PostCard> {
  // State to keep track of the current page in the carousel
  int _currentPage = 0;

  @override
  Widget build(BuildContext context) {
    // THE FIX: Access the media list directly, no need for jsonDecode here
    final List<dynamic> mediaList =  widget.postData['media'] as List<dynamic>? ?? [];
    final hasMedia = mediaList.isNotEmpty;

    final String baseUrl = defaultTargetPlatform == TargetPlatform.android
        ? "http://10.0.2.2:5000"
        : "http://127.0.0.1:5000";

    // Use .toString() for safety on each value
    final avatarUrl =
        widget.postData['avatarUrl']?.toString() ??
        'https://via.placeholder.com/150.png?text=N/A';
    final title = widget.postData['title']?.toString() ?? 'Untitled Post';
    final posterName = widget.postData['posterName']?.toString() ?? 'Unknown';
    final views = widget.postData['views']?.toString() ?? '0';
    final postedTime = widget.postData['postedTime']?.toString() ?? 'long ago';

    // Note: aspectRatio is calculated per media item in the PageView.builder below

    return Card(
      elevation: 0,
      margin: const EdgeInsets.all(0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (hasMedia)
            Column(
              children: [
                SizedBox(
                  height: 300,
                  child: PageView.builder(
                    itemCount: mediaList.length,
                    onPageChanged: (index) =>
                        setState(() => _currentPage = index),
                        itemBuilder: (context, index) {
                            final mediaItem = mediaList[index] as Map<String, dynamic>;

                            // THE CHANGE: Use the new MediaPlayer widget
                            return MediaPlayer(
                                mediaData: mediaItem,
                                baseUrl: baseUrl,
                            );
                        },
                  ),
                ),
                if (mediaList.length > 1)
                  _buildCarouselIndicator(mediaList.length),
              ],
            ),
          Padding(
            padding: const EdgeInsets.fromLTRB(12, 12, 12, 8),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CircleAvatar(
                  radius: 20,
                  backgroundImage: NetworkImage(avatarUrl),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        "$posterName • $views views • $postedTime",
                        style: const TextStyle(
                          color: Colors.grey,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          // NEW: Engagement buttons row
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 4.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    IconButton(
                      icon: const Icon(EvaIcons.arrowUpwardOutline),
                      onPressed: () {
                        print('Like button tapped');
                      },
                    ),
                    IconButton(
                      icon: const Icon(EvaIcons.arrowDownwardOutline),
                      onPressed: () {
                        print('Dislike button tapped');
                      },
                    ),
                    IconButton(
                      icon: const Icon(EvaIcons.messageSquareOutline),
                      onPressed: () {
                        // THE CHANGE: Show the comments modal as a bottom sheet
                        showModalBottomSheet(
                          context: context,
                          isScrollControlled:
                              true, // Allows the sheet to be full screen
                          builder: (context) {
                            // We use a fraction of the screen height
                            return FractionallySizedBox(
                              heightFactor: 0.9,
                              child: Container(
                                padding: const EdgeInsets.all(16),
                                child: const Text(
                                  'Comments Modal - Coming Soon',
                                ),
                              ),
                            );
                          },
                        );
                      },
                    ),
                    IconButton(
                      icon: const Icon(EvaIcons.bookmarkOutline),
                      onPressed: () {
                        showModalBottomSheet(
                          context: context,
                          builder: (context) => const AddToPlaylistModal(),
                        );
                      },
                    ),
                  ],
                ),
                IconButton(
                  icon: const Icon(EvaIcons.shareOutline),
                  onPressed: () {
                    print('Share button tapped');
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Helper widget to build the carousel dots
  Widget _buildCarouselIndicator(int itemCount) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(itemCount, (index) {
        return Container(
          width: 8.0,
          height: 8.0,
          margin: const EdgeInsets.symmetric(vertical: 10.0, horizontal: 2.0),
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: _currentPage == index
                ? Colors.teal
                : Colors.grey.withValues(alpha: 0.5),
          ),
        );
      }),
    );
  }
}
