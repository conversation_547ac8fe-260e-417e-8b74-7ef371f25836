import 'dart.io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:video_player/video_player.dart';

class MediaPreviewTile extends StatefulWidget {
  final XFile mediaFile;
  final VoidCallback onRemove;

  const MediaPreviewTile({
    super.key,
    required this.mediaFile,
    required this.onRemove,
  });

  @override
  State<MediaPreviewTile> createState() => _MediaPreviewTileState();
}

class _MediaPreviewTileState extends State<MediaPreviewTile> {
  VideoPlayerController? _videoController;
  bool _isVideo = false;

  @override
  void initState() {
    super.initState();
    // THE FIX: Check the MIME type to reliably identify videos.
    if (widget.mediaFile.mimeType?.startsWith('video/') ?? false) {
      _isVideo = true;
      if (kIsWeb) {
        _videoController = VideoPlayerController.networkUrl(Uri.parse(widget.mediaFile.path));
      } else {
        _videoController = VideoPlayerController.file(File(widget.mediaFile.path));
      }
      _videoController!.initialize().then((_) => setState(() {}));
    }
  }

  @override
  void dispose() {
    _videoController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(right: 8.0),
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(8.0),
            child: buildPreviewWidget(),
          ),
          Positioned(
            top: -8,
            right: -8,
            child: GestureDetector(
              onTap: widget.onRemove,
              child: Container(
                decoration: const BoxDecoration(
                  color: Colors.black54,
                  shape: BoxShape.circle,
                ),
                child: const Icon(Icons.close, color: Colors.white, size: 18),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget buildPreviewWidget() {
    if (_isVideo) {
      return (_videoController?.value.isInitialized ?? false)
          ? SizedBox(
              width: 100,
              height: 100,
              child: VideoPlayer(_videoController!),
            )
          : Container(
              width: 100,
              height: 100,
              color: Colors.black,
              child: const Center(child: CircularProgressIndicator()),
            );
    } else {
      return kIsWeb
          ? Image.network(widget.mediaFile.path, width: 100, height: 100, fit: BoxFit.cover)
          : Image.file(File(widget.mediaFile.path), width: 100, height: 100, fit: BoxFit.cover);
    }
  }
}