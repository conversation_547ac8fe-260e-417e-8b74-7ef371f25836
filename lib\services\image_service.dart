// import 'dart:io';
// import 'package:flutter/foundation.dart';
// import 'package:http/http.dart' as http;

// class ImageService {
//   static const String _baseUrl = kIsWeb || Platform.isIOS
//       ? "http://127.0.0.1:5000"
//       : "http://10.0.2.2:5000";

//   static const String _placeholderImageUrl =
//       'https://picsum.photos/400/300?grayscale&blur=1';

//   static const String _errorImageUrl =
//       'https://picsum.photos/400/300?grayscale';

//   /// Constructs a proper image URL from a photo path
//   /// Returns a placeholder URL if the path is invalid or the image is not accessible
//   static Future<String> getImageUrl(String? photoPath) async {
//     if (photoPath == null || photoPath.isEmpty) {
//       return _placeholderImageUrl;
//     }

//     // Construct the full URL
//     final imageUrl = '$_baseUrl/$photoPath';

//     // For now, return error placeholder since we know the uploads endpoint is not configured
//     // In the future, when the backend is fixed, we can enable the URL validation below
//     return _errorImageUrl;

//     // TODO: Uncomment this when the backend uploads endpoint is properly configured
//     /*
//     try {
//       // Check if the image URL is accessible
//       final response = await http.head(Uri.parse(imageUrl));
//       if (response.statusCode == 200) {
//         return imageUrl;
//       } else {
//         print('Image not accessible: $imageUrl (Status: ${response.statusCode})');
//         return _errorImageUrl;
//       }
//     } catch (e) {
//       print('Error checking image URL: $imageUrl - $e');
//       return _errorImageUrl;
//     }
//     */
//   }

//   /// Synchronous version that returns the constructed URL without validation
//   /// Use this when you want to let the Image.network widget handle the error
//   static String getImageUrlSync(String? photoPath) {
//     if (photoPath == null || photoPath.isEmpty) {
//       return _placeholderImageUrl;
//     }
//     return '$_baseUrl/$photoPath';
//   }

//   /// Returns appropriate placeholder URLs
//   static String get placeholderImageUrl => _placeholderImageUrl;
//   static String get errorImageUrl => _errorImageUrl;
// }
