import 'dart:ui';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'package:wicker/services/places_service.dart';
import 'package:wicker/widgets/custom_map_marker.dart';
import 'package:wicker/widgets/explore_post_card.dart';
import 'package:wicker/widgets/place_detail_card.dart';
import 'package:eva_icons_flutter/eva_icons_flutter.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:wicker/screens/detail_scroll_viewer.dart';

class ExploreScreen extends StatefulWidget {
  const ExploreScreen({super.key});

  @override
  _ExploreScreenState createState() => _ExploreScreenState();
}

class _ExploreScreenState extends State<ExploreScreen> {
  final PlacesService _placesService = PlacesService();
  late Future<List<Map<String, dynamic>>> _placesFuture;

  bool _isMapView = true;
  Map<String, dynamic>? _selectedPlace;
  // NEW: State for the active category filter
  String? _activeFilter;

  final List<Map<String, dynamic>> _categories = [
    {'name': 'All', 'icon': Icons.public, 'color': Colors.grey},
    {
      'name': 'Restaurant',
      'icon': Icons.restaurant_menu,
      'color': Colors.orange,
    },
    {'name': 'Cafe', 'icon': Icons.local_cafe, 'color': Colors.blue},
    {'name': 'Shop', 'icon': Icons.store, 'color': Colors.green},
    {'name': 'Art', 'icon': Icons.palette, 'color': Colors.purple},
  ];

  @override
  void initState() {
    super.initState();
    _placesFuture = _placesService.getPlaces();
  }

  /// Builds the list of markers based on the active filter
  List<Marker> _buildMarkers(List<Map<String, dynamic>> places) {
    // Filter places if an active filter is set
    final filteredPlaces = _activeFilter == null || _activeFilter == 'All'
        ? places
        : places.where((p) => p['category'] == _activeFilter).toList();

    return filteredPlaces.map((place) {
      final coords = place['location']['coordinates'];
      return Marker(
        width: 120.0,
        height: 100.0,
        point: LatLng(coords[1], coords[0]),
        child: GestureDetector(
          onTap: () => setState(() => _selectedPlace = place),
          child: CustomMapMarker(category: place['category']),
        ),
      );
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Explore',
          style: TextStyle(color: Colors.black, fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          IconButton(
            icon: Icon(
              _isMapView ? EvaIcons.gridOutline : EvaIcons.mapOutline,
              color: Colors.black,
            ),
            onPressed: () => setState(() => _isMapView = !_isMapView),
          ),
        ],
      ),
      extendBodyBehindAppBar: true,
      body: FutureBuilder<List<Map<String, dynamic>>>(
        future: _placesFuture,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }
          if (snapshot.hasError) {
            return Center(child: Text('Error: ${snapshot.error}'));
          }
          if (!snapshot.hasData || snapshot.data!.isEmpty) {
            return const Center(child: Text('No places to explore yet.'));
          }

          final allPlaces = snapshot.data!;
          final markers = _buildMarkers(allPlaces);
          final groupedData = _groupPlacesByCategory(allPlaces);

          if (_isMapView) {
            // THE FIX: Wrap the view in a LayoutBuilder
            return LayoutBuilder(
              builder: (context, constraints) {
                return _buildInteractiveMapView(markers, constraints);
              },
            );
          } else {
            return _buildDiscoveryGrid(allPlaces, groupedData);
          }
        },
      ),
    );
  }

  /// The interactive map view now accepts constraints from the LayoutBuilder
  Widget _buildInteractiveMapView(
    List<Marker> markers,
    BoxConstraints constraints,
  ) {
    // We get the max height from the constraints
    final maxHeight = constraints.maxHeight;

    return Stack(
      children: [
        AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          // Now it animates between two FINITE heights
          height: _selectedPlace == null ? maxHeight : maxHeight * 0.65,
          child: _buildMapWithUI(markers),
        ),
        if (_selectedPlace != null)
          Align(
            alignment: Alignment.bottomCenter,
            child: PlaceDetailCard(
              placeData: _selectedPlace!,
              onClose: () => setState(() => _selectedPlace = null),
            ),
          ),
      ],
    );
  }

  /// Builds the map and its overlayed UI elements
  Widget _buildMapWithUI(List<Marker> markers) {
    // THE FIX: Wrap the map in a Container to apply the border
    return Container(
      margin: const EdgeInsets.all(4), // Give some space for the border to show
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(18.0),
        border: Border.all(
          color: Colors.black,
          width: 2,
        ), // The thick black border
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(
          20.0,
        ), // Slightly smaller radius to not overlap the border
        child: Stack(
          children: [
            FlutterMap(
              options: MapOptions(
                initialCenter: const LatLng(5.6037, -0.1870),
                initialZoom: 12.0,
                interactionOptions: const InteractionOptions(
                  flags: InteractiveFlag.pinchZoom | InteractiveFlag.drag,
                ),
              ),
              children: [
                TileLayer(
                  urlTemplate:
                      'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
                ),
                MarkerLayer(markers: markers),
              ],
            ),
            Positioned(
              top: 120,
              left: 0,
              right: 0,
              child: SizedBox(
                height: 50,
                child: ListView(
                  scrollDirection: Axis.horizontal,
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  children: _categories.map((category) {
                    final bool isSelected = _activeFilter == category['name'];
                    return Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 4.0),
                      child: FilterChip(
                        label: Text(category['name']),
                        avatar: Icon(
                          category['icon'],
                          color: isSelected ? Colors.white : Colors.black87,
                        ),
                        selected: isSelected,
                        onSelected: (selected) {
                          setState(() {
                            _activeFilter = isSelected
                                ? null
                                : category['name'];
                          });
                        },
                        backgroundColor: category['color'].withOpacity(0.2),
                        selectedColor: category['color'],
                        labelStyle: TextStyle(
                          color: isSelected ? Colors.white : Colors.black87,
                        ),
                        showCheckmark: false,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                          side: BorderSide(
                            color: isSelected
                                ? Colors.transparent
                                : Colors.black,
                            width: 2,
                          ),
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// The masonry grid view
  Widget _buildDiscoveryGrid(
    List<Map<String, dynamic>> allPosts,
    Map<String, List<Map<String, dynamic>>> groupedData,
  ) {
    final String baseUrl = defaultTargetPlatform == TargetPlatform.android
        ? "http://10.0.2.2:5000"
        : "http://127.0.0.1:5000";

    return MasonryGridView.count(
      crossAxisCount: 2,
      mainAxisSpacing: 8,
      crossAxisSpacing: 8,
      padding: const EdgeInsets.only(top: 100, left: 12, right: 12, bottom: 12),
      itemCount: allPosts.length,
      itemBuilder: (context, index) {
        final post = allPosts[index];
        if (post['photos'] != null && (post['photos'] as List).isNotEmpty) {
          String imagePath = post['photos'][0];
          String correctedPath = imagePath.replaceAll('\\', '/');
          post['imageUrl'] = '$baseUrl/$correctedPath';
        }
        return AspectRatio(
          aspectRatio: index % 2 == 0 ? 1 / 1.2 : 1 / 1.5,
          child: GestureDetector(
            onTap: () {
              final category = post['category'] as String? ?? 'Other';
              final categoryList = groupedData[category]!;
              final itemIndex = categoryList.indexOf(post);
              final categoryIndex = groupedData.keys.toList().indexOf(category);

              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => DetailScrollViewer(
                    allCategoriesData: groupedData.map(
                      (key, value) => MapEntry(
                        key,
                        value
                            .map(
                              (e) => e.map((k, v) => MapEntry(k, v.toString())),
                            )
                            .toList(),
                      ),
                    ),
                    initialCategoryIndex: categoryIndex,
                    initialItemIndex: itemIndex,
                  ),
                ),
              );
            },
            child: ExplorePostCard(
              postData: post.map((k, v) => MapEntry(k, v.toString())),
            ),
          ),
        );
      },
    );
  }

  /// Helper to group places by category.
  Map<String, List<Map<String, dynamic>>> _groupPlacesByCategory(
    List<Map<String, dynamic>> places,
  ) {
    final Map<String, List<Map<String, dynamic>>> grouped = {};
    for (var place in places) {
      final category = place['category'] as String? ?? 'Other';
      if (grouped.containsKey(category)) {
        grouped[category]!.add(place);
      } else {
        grouped[category] = [place];
      }
    }
    return grouped;
  }
}
