import 'package:flutter/material.dart';
import 'package:neubrutalism_ui/neubrutalism_ui.dart';

class AddToPlaylistModal extends StatefulWidget {
  // In a real app, you would pass in the list of the user's playlists
  const AddToPlaylistModal({super.key});

  @override
  _AddToPlaylistModalState createState() => _AddToPlaylistModalState();
}

class _AddToPlaylistModalState extends State<AddToPlaylistModal> {
  final _customNameController = TextEditingController();
  String? _selectedPlaylist;

  // Mock data for the user's playlists
  final List<String> _userPlaylists = [
    'Accra Weekend Trip',
    'Favorite Restaurants',
  ];

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Save to...',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          // A dropdown to select the playlist
          DropdownButtonFormField<String>(
            value: _selectedPlaylist,
            hint: const Text('Choose a playlist'),
            onChanged: (value) => setState(() => _selectedPlaylist = value),
            items: _userPlaylists.map((playlistName) {
              return DropdownMenuItem(
                value: playlistName,
                child: Text(playlistName),
              );
            }).toList(),
          ),
          const SizedBox(height: 16),
          // The optional field for the custom name (alias)
          NeuSearchBar(
            searchController: _customNameController,
            hintText: 'Save as (optional)',
            keyboardType: TextInputType.text,
            searchBarHeight: 60,
          ),
          const SizedBox(height: 24),
          NeuTextButton(
            onPressed: () {
              print(
                'Saving to playlist: $_selectedPlaylist with Custom name: ${_customNameController.text}',
              );
              Navigator.pop(context);
            },
            buttonColor: Colors.teal,
            buttonHeight: 60,
            enableAnimation: true,
            text: Text(
              'Add to Playlist',
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
