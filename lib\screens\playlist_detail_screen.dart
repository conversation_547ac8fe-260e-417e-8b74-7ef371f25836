import 'package:flutter/material.dart';
import 'package:wicker/services/playlist_service.dart';
import 'package:wicker/widgets/post_card.dart';

class PlaylistDetailScreen extends StatefulWidget {
  final String playlistId;
  final String playlistName;

  const PlaylistDetailScreen({
    super.key,
    required this.playlistId,
    required this.playlistName,
  });

  @override
  State<PlaylistDetailScreen> createState() => _PlaylistDetailScreenState();
}

class _PlaylistDetailScreenState extends State<PlaylistDetailScreen> {
  final PlaylistService _playlistService = PlaylistService();
  late Future<Map<String, dynamic>> _playlistFuture;

  @override
  void initState() {
    super.initState();
    _playlistFuture = _playlistService.getPlaylistDetails(widget.playlistId);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text(widget.playlistName)),
      body: FutureBuilder<Map<String, dynamic>>(
        future: _playlistFuture,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }
          if (snapshot.hasError) {
            return Center(child: Text('Error: ${snapshot.error}'));
          }

          final items =
              snapshot.data?['populated_items'] as List<dynamic>? ?? [];

          if (items.isEmpty) {
            return const Center(child: Text('This playlist is empty.'));
          }

          return ListView.builder(
            itemCount: items.length,
            itemBuilder: (context, index) {
              final post = Map<String, String>.from(
                items[index].map((k, v) => MapEntry(k, v.toString())),
              );
              // You will need to process the post data here to add imageUrl etc.
              return PostCard(postData: post);
            },
          );
        },
      ),
    );
  }
}
